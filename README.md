# AI Chat Interface - Enhanced Version

A sophisticated AI chat application designed for coding assistance with advanced features for software engineers.

## 🚀 New Features

### 1. AI Persona Enhancement
Configure the AI to behave as different types of software engineering experts:

- **Expert Software Engineer**: Provides detailed code analysis and solutions
- **Code Reviewer**: Focuses on code quality and best practices
- **Software Architect**: Emphasizes system design and scalability
- **Programming Mentor**: Educational approach for learning
- **Custom Persona**: Define your own AI behavior

**Features:**
- Customizable expertise areas (JavaScript, Python, React, Node.js, etc.)
- Response style options (Detailed, Concise, Step-by-step, Example-heavy)
- Custom system prompts for specialized use cases
- Persistent settings across sessions

### 2. Smart Code Integration
Automatically detect and apply AI-suggested code changes:

- **Intelligent Code Detection**: Automatically identifies code suggestions in AI responses
- **Visual Code Preview**: See exactly what changes will be applied
- **One-Click Application**: Apply code changes directly to the code editor
- **Multi-file Support**: Handle modifications across multiple files
- **Undo/Redo System**: Safely revert changes if needed
- **Language Detection**: Automatically sets the correct programming language

**How it works:**
1. AI provides code suggestions in responses
2. "Apply Code" button appears when code is detected
3. Preview changes before applying
4. Apply directly to the integrated code editor

### 3. Enhanced Chat History Management
Comprehensive export and import functionality:

**Export Features:**
- **Multiple Formats**: JSON (complete data), Markdown (readable), Plain Text
- **Date Range Filtering**: Export chats from specific time periods
- **Selective Export**: Choose specific conversations to export
- **Bulk Export**: Export all chat history at once
- **Context Preservation**: Maintains code snippets and conversation context

**Import Features:**
- **JSON Import**: Restore previously exported chat data
- **Duplicate Detection**: Automatically handles duplicate conversations
- **Merge Capability**: Import into existing chat history without conflicts

## 🎨 Design Features

- **Minimalistic Flat Design**: Clean, square elements without rounded corners
- **Dark/Light Mode**: Seamless theme switching with system preference detection
- **Responsive Layout**: Optimized for desktop, tablet, and mobile devices
- **Custom Icons**: Uses local SVG icons for consistent branding
- **Code Editor Integration**: Full-featured code editor with syntax highlighting

## 🛠️ Technical Architecture

### Core Components
- `script.js` - Main application logic and Ollama integration
- `ai-persona.js` - AI persona management and system prompt generation
- `code-integration.js` - Smart code detection and application
- `export-import.js` - Chat history export/import functionality
- `style.css` - Comprehensive styling with CSS custom properties

### Key Technologies
- **Ollama API**: Local LLM integration
- **CodeMirror**: Advanced code editor
- **Highlight.js**: Syntax highlighting for code blocks
- **Marked.js**: Markdown parsing and rendering
- **LocalStorage**: Persistent data storage

## 🚀 Getting Started

1. **Install Ollama**: Download from [ollama.ai](https://ollama.ai/download)
2. **Start Ollama Service**: Ensure it's running on `http://localhost:11434`
3. **Download a Model**: Run `ollama pull mistral` or your preferred model
4. **Open the Application**: Launch `index.html` in your browser
5. **Select a Model**: Choose your AI model from the dropdown
6. **Configure Persona**: Click the persona settings icon to customize AI behavior

## 📱 Usage Guide

### Setting Up AI Persona
1. Click the gear icon (👤⚙️) in the header
2. Choose from predefined personas or create custom
3. Select expertise areas relevant to your work
4. Choose response style preference
5. Save settings for future sessions

### Using Smart Code Integration
1. Ask the AI for code suggestions or solutions
2. Look for the magic wand icon (✨) when code is detected
3. Click to preview the suggested changes
4. Apply changes directly to the code editor
5. Use undo if you need to revert changes

### Managing Chat History
1. Click the download icon (📥) in the header
2. Choose export format and date range
3. Select specific chats or export all
4. Download the exported file
5. Use the import feature to restore chat history

## 🔧 Configuration

### AI Persona Settings
- **Persona Type**: Choose the AI's role and expertise focus
- **Expertise Areas**: Select relevant programming languages and technologies
- **Response Style**: Control how detailed or concise responses should be
- **Custom Prompts**: Define specific instructions for the AI

### Export/Import Options
- **JSON Format**: Complete data with all metadata
- **Markdown Format**: Human-readable conversation format
- **Text Format**: Simple plain text export
- **Date Filtering**: Export conversations from specific time periods

## 🎯 Best Practices

### For Code Assistance
1. Set appropriate expertise areas in persona settings
2. Use specific, detailed questions for better responses
3. Include relevant code context in your questions
4. Review AI suggestions before applying to code editor

### For Chat Management
1. Regularly export important conversations
2. Use descriptive titles for better organization
3. Filter exports by date for project-specific backups
4. Import previous conversations when switching devices

## 🔒 Privacy & Security

- **Local Processing**: All data stored locally in browser
- **No Cloud Sync**: Chat history remains on your device
- **Ollama Integration**: Uses local AI models for privacy
- **Export Control**: You control what data is exported and when

## 🤝 Contributing

This application is designed to be extensible. Key areas for enhancement:
- Additional AI personas for specialized domains
- Enhanced code integration with more programming languages
- Advanced export formats and filtering options
- Integration with external code repositories

## 📄 License

This project is open source and available under the MIT License.
