// Console debug script for AI Persona button
// Copy and paste this into the browser console to debug the AI Persona button

console.log('🔍 Starting AI Persona Button Debug...');

// Test 1: Check if elements exist
console.log('\n📋 Test 1: Checking DOM Elements');
const personaButton = document.getElementById('ai-persona-toggle');
const personaPanel = document.getElementById('ai-persona-panel');

console.log('Persona Button:', personaButton);
console.log('Persona Panel:', personaPanel);

if (personaButton) {
    console.log('✅ Persona button found');
    console.log('Button classes:', personaButton.className);
    console.log('Button style:', personaButton.style.cssText);
    console.log('Button disabled:', personaButton.disabled);
} else {
    console.log('❌ Persona button NOT found');
}

if (personaPanel) {
    console.log('✅ Persona panel found');
    console.log('Panel classes:', personaPanel.className);
    console.log('Panel style:', personaPanel.style.cssText);
} else {
    console.log('❌ Persona panel NOT found');
}

// Test 2: Check event listeners
console.log('\n🎯 Test 2: Testing Event Listeners');
if (personaButton) {
    // Get all event listeners (this might not work in all browsers)
    console.log('Attempting to check event listeners...');
    
    // Try to manually trigger click
    console.log('Manually triggering click event...');
    personaButton.click();
    
    setTimeout(() => {
        if (personaPanel && personaPanel.classList.contains('open')) {
            console.log('✅ Manual click worked! Panel is now open');
        } else {
            console.log('❌ Manual click did not open panel');
        }
    }, 100);
}

// Test 3: Check managers
console.log('\n🏗️ Test 3: Checking Managers');
console.log('AI Persona Manager:', window.aiPersonaManager);
console.log('Export/Import Manager:', window.exportImportManager);

// Test 4: Force button to work
console.log('\n🔧 Test 4: Force Button to Work');
if (personaButton && personaPanel) {
    console.log('Setting up forced event listener...');
    
    // Remove all existing event listeners by cloning the button
    const newButton = personaButton.cloneNode(true);
    personaButton.parentNode.replaceChild(newButton, personaButton);
    
    // Add new event listener
    newButton.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('🎯 FORCED: Persona button clicked!');
        
        // Toggle panel
        personaPanel.classList.toggle('open');
        console.log('👤 FORCED: Panel toggled, now:', personaPanel.classList.contains('open') ? 'OPEN' : 'CLOSED');
        
        // Initialize manager if needed
        if (!window.aiPersonaManager && typeof AIPersonaManager !== 'undefined') {
            window.aiPersonaManager = new AIPersonaManager();
            window.aiPersonaManager.loadSettings();
            console.log('🚀 FORCED: AI Persona Manager initialized');
        }
    });
    
    console.log('✅ Forced event listener added. Try clicking the button now!');
} else {
    console.log('❌ Cannot set up forced listener - elements missing');
}

// Test 5: Alternative button creation
console.log('\n🆕 Test 5: Creating Alternative Button');
const altButton = document.createElement('button');
altButton.innerHTML = '🔧 TEST PERSONA BUTTON';
altButton.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 9999;
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
`;

altButton.addEventListener('click', function() {
    console.log('🧪 ALTERNATIVE: Test button clicked!');
    if (personaPanel) {
        personaPanel.classList.toggle('open');
        console.log('👤 ALTERNATIVE: Panel toggled via test button');
    }
});

document.body.appendChild(altButton);
console.log('✅ Alternative test button added to top-right corner');

console.log('\n🎯 Debug completed! Check the results above and try the buttons.');
