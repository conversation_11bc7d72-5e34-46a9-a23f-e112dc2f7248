// AI Persona Management Module
class AIPersonaManager {
    constructor() {
        this.currentPersona = 'expert-engineer';
        this.customPrompt = '';
        this.expertiseAreas = ['javascript', 'python', 'react', 'nodejs', 'databases', 'devops'];
        this.responseStyle = 'detailed';
        this.currentProvider = 'ollama';
        this.apiKeys = {};
        
        this.personas = {
            'expert-engineer': {
                name: 'Expert Software Engineer',
                systemPrompt: `You are an expert software engineer with deep knowledge across multiple programming languages and technologies. You provide detailed, accurate, and practical solutions to coding problems. Your responses should:

- Analyze code thoroughly and identify potential issues
- Provide step-by-step solutions with clear explanations
- Follow software engineering best practices
- Suggest optimizations and improvements
- Include relevant code examples
- Consider security, performance, and maintainability
- Explain complex concepts in an understandable way

Focus on being helpful, accurate, and educational in your responses.`
            },
            'code-reviewer': {
                name: 'Code Reviewer',
                systemPrompt: `You are a senior code reviewer focused on code quality, best practices, and maintainability. Your responses should:

- Perform thorough code analysis
- Identify bugs, security vulnerabilities, and performance issues
- Suggest improvements for readability and maintainability
- Recommend design patterns and architectural improvements
- Provide specific, actionable feedback
- Explain the reasoning behind your suggestions
- Consider team collaboration and code standards

Be constructive and educational in your feedback.`
            },
            'architect': {
                name: 'Software Architect',
                systemPrompt: `You are a software architect with expertise in system design, scalability, and technology selection. Your responses should:

- Focus on high-level system design and architecture
- Consider scalability, reliability, and performance
- Recommend appropriate technologies and patterns
- Think about long-term maintainability and evolution
- Address cross-cutting concerns like security and monitoring
- Provide architectural diagrams and documentation when helpful
- Consider business requirements and constraints

Think strategically about software systems and their evolution.`
            },
            'mentor': {
                name: 'Programming Mentor',
                systemPrompt: `You are a patient and encouraging programming mentor focused on education and skill development. Your responses should:

- Break down complex concepts into digestible parts
- Provide learning resources and next steps
- Encourage experimentation and learning from mistakes
- Adapt explanations to the learner's level
- Ask clarifying questions to understand the context
- Provide multiple approaches to solve problems
- Focus on building understanding, not just providing answers

Be supportive, patient, and focused on long-term learning.`
            },
            'custom': {
                name: 'Custom Persona',
                systemPrompt: ''
            }
        };
        
        this.loadSettings();
        this.initializeEventListeners(); // Initialize event listeners
    }
    
    initializeEventListeners() {
        // Add a small delay to ensure DOM elements are available
        setTimeout(() => {
            this.setupEventListeners();
        }, 100);
    }

    setupEventListeners() {
        // Note: Panel toggle is handled by main script to avoid conflicts
        // Only set up internal panel controls here
        
        // Settings controls
        const personaSelect = document.getElementById('persona-select');
        const responseStyleSelect = document.getElementById('response-style');
        const customPromptTextarea = document.getElementById('custom-prompt');
        const savePersonaButton = document.getElementById('save-persona');
        const resetPersonaButton = document.getElementById('reset-persona');
        const providerSelect = document.getElementById('provider-select');
        const apiKeyInputs = document.querySelectorAll('.api-key-input');
        const testConnectionButtons = document.querySelectorAll('.test-connection-btn');
        
        if (personaSelect) {
            personaSelect.addEventListener('change', (e) => {
                this.currentPersona = e.target.value;
                this.updateCustomPromptVisibility();
                this.updateUI();
            });
        }
        
        if (responseStyleSelect) {
            responseStyleSelect.addEventListener('change', (e) => {
                this.responseStyle = e.target.value;
            });
        }
        
        if (customPromptTextarea) {
            customPromptTextarea.addEventListener('input', (e) => {
                this.customPrompt = e.target.value;
            });
        }
        
        if (savePersonaButton) {
            savePersonaButton.addEventListener('click', () => {
                this.saveSettings();
                this.showNotification('Persona settings saved successfully!');
            });
        }
        
        if (resetPersonaButton) {
            resetPersonaButton.addEventListener('click', () => {
                this.resetToDefault();
                this.showNotification('Persona settings reset to default!');
            });
        }
        
        // Expertise area checkboxes
        const expertiseCheckboxes = document.querySelectorAll('.checkbox-group input[type="checkbox"]');
        expertiseCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateExpertiseAreas();
            });
        });

        // Provider selection
        if (providerSelect) {
            providerSelect.addEventListener('change', (e) => {
                console.log('Provider changed to:', e.target.value);
                this.currentProvider = e.target.value;
                this.updateProviderUI();
                this.saveSettings(); // Save immediately when provider changes
            });
        }

        // API key inputs
        apiKeyInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                const provider = e.target.dataset.provider;
                this.apiKeys[provider] = e.target.value;
            });
        });

        // Test connection buttons
        testConnectionButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const provider = e.target.dataset.provider;
                this.testConnection(provider);
            });
        });
    }
    
    updateExpertiseAreas() {
        const checkboxes = document.querySelectorAll('.checkbox-group input[type="checkbox"]:checked');
        this.expertiseAreas = Array.from(checkboxes).map(cb => cb.value);
    }

    updateProviderUI() {
        console.log('Updating provider UI for:', this.currentProvider);

        // Show/hide API key sections based on selected provider
        const apiKeySections = document.querySelectorAll('.api-key-section');
        console.log('Found API key sections:', apiKeySections.length);

        apiKeySections.forEach(section => {
            const provider = section.dataset.provider;
            const shouldShow = provider === this.currentProvider;
            section.style.display = shouldShow ? 'block' : 'none';
            console.log(`Provider ${provider}: ${shouldShow ? 'shown' : 'hidden'}`);
        });

        // Update model selection if provider manager is available
        if (window.aiProviderManager && window.loadModels) {
            console.log('Loading models for provider:', this.currentProvider);
            window.loadModels();
        }
    }

    async testConnection(provider) {
        const button = document.querySelector(`[data-provider="${provider}"].test-connection-btn`);
        const originalText = button.textContent;

        button.textContent = 'Testing...';
        button.disabled = true;

        try {
            if (window.aiProviderManager) {
                const isValid = await window.aiProviderManager.testConnection(provider, this.apiKeys[provider]);
                if (isValid) {
                    this.showNotification(`✅ ${provider} connection successful!`);
                    button.textContent = '✅ Connected';

                    // Refresh models if this is the current provider
                    if (provider === this.currentProvider && window.loadModels) {
                        setTimeout(() => {
                            window.loadModels();
                        }, 500);
                    }
                } else {
                    this.showNotification(`❌ ${provider} connection failed. Please check your API key.`);
                    button.textContent = '❌ Failed';
                }
            }
        } catch (error) {
            console.error(`Error testing ${provider} connection:`, error);
            this.showNotification(`❌ Error testing ${provider} connection: ${error.message}`);
            button.textContent = '❌ Error';
        }

        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
        }, 3000);
    }
    
    updateCustomPromptVisibility() {
        const customPromptGroup = document.getElementById('custom-prompt').closest('.setting-group');
        if (customPromptGroup) {
            customPromptGroup.style.display = this.currentPersona === 'custom' ? 'block' : 'none';
        }
    }
    
    updateUI() {
        const personaSelect = document.getElementById('persona-select');
        const responseStyleSelect = document.getElementById('response-style');
        const customPromptTextarea = document.getElementById('custom-prompt');
        const providerSelect = document.getElementById('provider-select');

        if (personaSelect) personaSelect.value = this.currentPersona;
        if (responseStyleSelect) responseStyleSelect.value = this.responseStyle;
        if (customPromptTextarea) customPromptTextarea.value = this.customPrompt;
        if (providerSelect) providerSelect.value = this.currentProvider;

        // Update expertise checkboxes
        const expertiseCheckboxes = document.querySelectorAll('.checkbox-group input[type="checkbox"]');
        expertiseCheckboxes.forEach(checkbox => {
            checkbox.checked = this.expertiseAreas.includes(checkbox.value);
        });

        // Update API key inputs
        const apiKeyInputs = document.querySelectorAll('.api-key-input');
        apiKeyInputs.forEach(input => {
            const provider = input.dataset.provider;
            if (this.apiKeys[provider]) {
                input.value = this.apiKeys[provider];
            }
        });

        this.updateCustomPromptVisibility();
        this.updateProviderUI();
    }
    
    getSystemPrompt() {
        let basePrompt = '';
        
        if (this.currentPersona === 'custom' && this.customPrompt.trim()) {
            basePrompt = this.customPrompt;
        } else if (this.personas[this.currentPersona]) {
            basePrompt = this.personas[this.currentPersona].systemPrompt;
        }
        
        // Add expertise areas and response style
        const expertiseText = this.expertiseAreas.length > 0 
            ? `\n\nYour primary expertise areas include: ${this.expertiseAreas.join(', ')}.`
            : '';
            
        const styleText = this.getResponseStylePrompt();
        
        return basePrompt + expertiseText + styleText;
    }
    
    getResponseStylePrompt() {
        const styles = {
            'detailed': '\n\nProvide detailed explanations with comprehensive examples and context.',
            'concise': '\n\nKeep responses concise and direct, focusing on the essential information.',
            'step-by-step': '\n\nBreak down solutions into clear, numbered steps that are easy to follow.',
            'examples': '\n\nInclude multiple practical examples and code samples in your responses.'
        };
        
        return styles[this.responseStyle] || styles['detailed'];
    }
    
    saveSettings() {
        const settings = {
            currentPersona: this.currentPersona,
            customPrompt: this.customPrompt,
            expertiseAreas: this.expertiseAreas,
            responseStyle: this.responseStyle,
            currentProvider: this.currentProvider,
            apiKeys: this.encryptApiKeys(this.apiKeys)
        };

        localStorage.setItem('ai-persona-settings', JSON.stringify(settings));
        console.log('AI Persona settings saved:', { ...settings, apiKeys: '[ENCRYPTED]' });
    }

    encryptApiKeys(apiKeys) {
        // Simple encryption for local storage (not production-grade)
        const encrypted = {};
        for (const [provider, key] of Object.entries(apiKeys)) {
            if (key) {
                encrypted[provider] = btoa(key); // Base64 encoding
            }
        }
        return encrypted;
    }

    decryptApiKeys(encryptedKeys) {
        // Simple decryption for local storage
        const decrypted = {};
        for (const [provider, encryptedKey] of Object.entries(encryptedKeys || {})) {
            try {
                decrypted[provider] = atob(encryptedKey); // Base64 decoding
            } catch (error) {
                console.warn(`Failed to decrypt API key for ${provider}`);
            }
        }
        return decrypted;
    }
    
    loadSettings() {
        const saved = localStorage.getItem('ai-persona-settings');
        if (saved) {
            try {
                const settings = JSON.parse(saved);
                this.currentPersona = settings.currentPersona || 'expert-engineer';
                this.customPrompt = settings.customPrompt || '';
                this.expertiseAreas = settings.expertiseAreas || ['javascript', 'python', 'react', 'nodejs', 'databases', 'devops'];
                this.responseStyle = settings.responseStyle || 'detailed';
                this.currentProvider = settings.currentProvider || 'ollama';
                this.apiKeys = this.decryptApiKeys(settings.apiKeys || {});
            } catch (error) {
                console.error('Error loading persona settings:', error);
            }
        }

        // Update UI after loading
        setTimeout(() => {
            this.updateUI();
            console.log('AI Persona settings loaded and UI updated');
        }, 100);
    }
    
    resetToDefault() {
        this.currentPersona = 'expert-engineer';
        this.customPrompt = '';
        this.expertiseAreas = ['javascript', 'python', 'react', 'nodejs', 'databases', 'devops'];
        this.responseStyle = 'detailed';
        this.currentProvider = 'ollama';
        this.apiKeys = {};
        this.updateUI();
        this.saveSettings();
    }
    
    showNotification(message) {
        // Create a simple notification
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: var(--shadow-lg);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize the AI Persona Manager
let aiPersonaManager;

// Don't auto-initialize - let the main script handle it
// This prevents conflicts with event listeners

function initializePersonaManager() {
    if (!aiPersonaManager) {
        aiPersonaManager = new AIPersonaManager();
        window.aiPersonaManager = aiPersonaManager; // Make it globally available
        console.log('AI Persona Manager initialized');
    }
    return aiPersonaManager;
}

// Expose the initialization function globally
window.initializePersonaManager = initializePersonaManager;
